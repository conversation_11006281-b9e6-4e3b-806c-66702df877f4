{"info": {"_postman_id": "e-commerce-golang-clean-architecture", "name": "E-commerce API - Golang Clean Architecture", "description": "Complete API collection for E-commerce system built with Golang and Clean Architecture", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "product_id", "value": "", "type": "string"}, {"key": "category_id", "value": "", "type": "string"}, {"key": "order_id", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('user_id', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('jwt_token', response.data.token);", "    pm.collectionVariables.set('user_id', response.data.user.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get User Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}}, "response": []}, {"name": "Update User Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"phone\": \"+1234567891\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}}, "response": []}, {"name": "Change Password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"password123\",\n    \"new_password\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/change-password", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "change-password"]}}, "response": []}]}, {"name": "Categories", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "categories"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Get Category Tree", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/tree", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "tree"]}}, "response": []}, {"name": "Get Root Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/categories/root", "host": ["{{base_url}}"], "path": ["api", "v1", "categories", "root"]}}, "response": []}, {"name": "Create Category (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('category_id', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Category\",\n    \"description\": \"A test category for API testing\",\n    \"slug\": \"test-category\",\n    \"is_active\": true,\n    \"sort_order\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/categories", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "categories"]}}, "response": []}]}, {"name": "Products", "item": [{"name": "Get Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "products"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/search?q=test&limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "search"], "query": [{"key": "q", "value": "test"}, {"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Create Product (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('product_id', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Product\",\n    \"description\": \"A test product for API testing\",\n    \"sku\": \"TEST-001\",\n    \"price\": 99.99,\n    \"stock\": 100,\n    \"category_id\": \"{{category_id}}\",\n    \"status\": \"active\",\n    \"is_digital\": false\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/products", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "products"]}}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "products", "{{product_id}}"]}}, "response": []}]}, {"name": "Shopping Cart", "item": [{"name": "Get Cart", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}}, "response": []}, {"name": "Add to Cart", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": \"{{product_id}}\",\n    \"quantity\": 2\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items"]}}, "response": []}, {"name": "Update Cart Item", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_id\": \"{{product_id}}\",\n    \"quantity\": 3\n}"}, "url": {"raw": "{{base_url}}/api/v1/cart/items", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items"]}}, "response": []}, {"name": "Remove from Cart", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/cart/items/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "cart", "items", "{{product_id}}"]}}, "response": []}, {"name": "Clear Cart", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/v1/cart", "host": ["{{base_url}}"], "path": ["api", "v1", "cart"]}}, "response": []}]}, {"name": "Orders", "item": [{"name": "Create Order", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('order_id', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"shipping_address\": {\n        \"first_name\": \"<PERSON>\",\n        \"last_name\": \"<PERSON><PERSON>\",\n        \"address1\": \"123 Main St\",\n        \"city\": \"New York\",\n        \"state\": \"NY\",\n        \"zip_code\": \"10001\",\n        \"country\": \"USA\",\n        \"phone\": \"+**********\"\n    },\n    \"payment_method\": \"credit_card\",\n    \"tax_rate\": 0.08,\n    \"shipping_cost\": 10.00,\n    \"discount_amount\": 0.00\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"]}}, "response": []}, {"name": "Get User Orders", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/orders?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "orders"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Get Order by ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}"]}}, "response": []}, {"name": "Cancel Order", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/orders/{{order_id}}/cancel", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "{{order_id}}", "cancel"]}}, "response": []}]}, {"name": "Admin", "item": [{"name": "Get All Users", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/admin/users?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "users"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Get All Orders", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/admin/orders?limit=10&offset=0", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "orders"], "query": [{"key": "limit", "value": "10"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Update Order Status", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/admin/orders/{{order_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "admin", "orders", "{{order_id}}", "status"]}}, "response": []}]}]}