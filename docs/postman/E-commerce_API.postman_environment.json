{"id": "e-commerce-environment", "name": "E-commerce API Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "jwt_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "product_id", "value": "", "type": "default", "enabled": true}, {"key": "category_id", "value": "", "type": "default", "enabled": true}, {"key": "order_id", "value": "", "type": "default", "enabled": true}, {"key": "cart_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-23T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}